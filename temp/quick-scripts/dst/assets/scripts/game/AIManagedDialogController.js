
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/game/AIManagedDialogController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '423f58DLGJCFIMOAtc3Vt+Q', 'AIManagedDialogController');
// scripts/game/AIManagedDialogController.ts

"use strict";
// AI托管中页面控制器
// 当玩家进入AI托管状态时显示，点击屏幕任何位置发送取消托管消息
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var WebSocketManager_1 = require("../net/WebSocketManager");
var MessageId_1 = require("../net/MessageId");
var Config_1 = require("../util/Config");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var AIManagedDialogController = /** @class */ (function (_super) {
    __extends(AIManagedDialogController, _super);
    function AIManagedDialogController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.boardBg = null; // 背景板
        _this.maskNode = null; // 遮罩节点，用于接收点击事件
        _this.isShowing = false; // 是否正在显示
        return _this;
    }
    AIManagedDialogController.prototype.start = function () {
        console.log("AIManagedDialogController start() 被调用");
        // 初始化时隐藏
        this.node.active = false;
        // 检查节点配置
        console.log("检查节点配置:");
        console.log("- node:", this.node ? this.node.name : "null");
        console.log("- boardBg:", this.boardBg ? this.boardBg.name : "null");
        console.log("- maskNode:", this.maskNode ? this.maskNode.name : "null");
        // 如果 boardBg 没有设置，尝试查找子节点
        if (!this.boardBg) {
            console.log("boardBg 未设置，尝试查找子节点...");
            var bgNode = this.node.getChildByName('bot_bg');
            if (bgNode) {
                this.boardBg = bgNode;
                console.log("找到 bot_bg 子节点，设置为 boardBg");
            }
            else {
                console.warn("未找到 bot_bg 子节点");
            }
        }
        // 为遮罩节点添加点击事件监听
        if (this.maskNode) {
            // 检查是否有 BlockInputEvents 组件，如果有则禁用它
            var blockInputEvents = this.maskNode.getComponent(cc.BlockInputEvents);
            if (blockInputEvents) {
                console.log("发现 BlockInputEvents 组件，将其禁用以允许点击事件");
                blockInputEvents.enabled = false;
            }
            this.maskNode.on(cc.Node.EventType.TOUCH_END, this.onMaskClick, this);
            console.log("遮罩节点点击事件已设置，节点名称:", this.maskNode.name);
            console.log("遮罩节点大小:", this.maskNode.width, "x", this.maskNode.height);
            console.log("遮罩节点位置:", this.maskNode.position);
        }
        else {
            console.warn("maskNode 未设置");
        }
        // 同时为主节点添加点击事件监听作为备用
        this.node.on(cc.Node.EventType.TOUCH_END, this.onNodeClick, this);
    };
    /**
     * 显示托管中页面
     */
    AIManagedDialogController.prototype.show = function () {
        console.log("AIManagedDialogController.show() 被调用");
        if (this.isShowing) {
            console.log("托管页面已经在显示中，跳过");
            return;
        }
        console.log("开始显示托管页面...");
        this.isShowing = true;
        this.node.active = true;
        // 禁用 BlockInputEvents 组件以允许点击事件
        if (this.maskNode) {
            var blockInputEvents = this.maskNode.getComponent(cc.BlockInputEvents);
            if (blockInputEvents) {
                blockInputEvents.enabled = false;
                console.log("显示时禁用 BlockInputEvents 组件");
            }
        }
        console.log("托管页面节点已激活，node.active =", this.node.active);
        // 初始化动画状态
        if (this.boardBg) {
            this.boardBg.scale = 0;
            this.boardBg.opacity = 0;
            console.log("背景板初始化完成");
        }
        else {
            console.warn("❌ boardBg 节点未设置");
        }
        // 执行显示动画
        this.playShowAnimation();
        console.log("托管页面显示动画已启动");
    };
    /**
     * 隐藏托管中页面
     */
    AIManagedDialogController.prototype.hide = function () {
        var _this = this;
        if (!this.isShowing) {
            return;
        }
        this.isShowing = false;
        // 重新启用 BlockInputEvents 组件（如果存在）
        if (this.maskNode) {
            var blockInputEvents = this.maskNode.getComponent(cc.BlockInputEvents);
            if (blockInputEvents) {
                blockInputEvents.enabled = true;
                console.log("重新启用 BlockInputEvents 组件");
            }
        }
        // 执行隐藏动画
        this.playHideAnimation(function () {
            _this.node.active = false;
        });
    };
    /**
     * 播放显示动画
     */
    AIManagedDialogController.prototype.playShowAnimation = function () {
        console.log("playShowAnimation 开始执行");
        if (!this.boardBg) {
            console.warn("❌ boardBg 为空，无法播放动画");
            return;
        }
        console.log("开始播放托管页面显示动画，动画时间:", Config_1.Config.dialogScaleTime);
        // 缩放和透明度动画
        cc.tween(this.boardBg)
            .to(Config_1.Config.dialogScaleTime, {
            scale: 1,
            opacity: 255
        }, {
            easing: 'backOut'
        })
            .call(function () {
            console.log("托管页面显示动画完成");
        })
            .start();
    };
    /**
     * 播放隐藏动画
     * @param callback 动画完成回调
     */
    AIManagedDialogController.prototype.playHideAnimation = function (callback) {
        if (!this.boardBg) {
            if (callback)
                callback();
            return;
        }
        // 缩放和透明度动画
        cc.tween(this.boardBg)
            .to(Config_1.Config.dialogScaleTime, {
            scale: 0,
            opacity: 0
        }, {
            easing: 'backIn'
        })
            .call(function () {
            if (callback)
                callback();
        })
            .start();
    };
    /**
     * 遮罩点击事件处理
     */
    AIManagedDialogController.prototype.onMaskClick = function () {
        console.log("遮罩节点被点击");
        this.handleClick();
    };
    /**
     * 主节点点击事件处理
     */
    AIManagedDialogController.prototype.onNodeClick = function () {
        console.log("主节点被点击");
        this.handleClick();
    };
    /**
     * 统一的点击处理逻辑
     */
    AIManagedDialogController.prototype.handleClick = function () {
        if (!this.isShowing) {
            console.log("托管页面未显示，忽略点击");
            return;
        }
        console.log("托管页面点击，发送取消AI托管消息");
        // 发送取消AI托管消息
        this.sendCancelAIManagement();
        // 立即隐藏页面（不等待服务器响应）
        this.hide();
    };
    /**
     * 发送取消AI托管消息
     */
    AIManagedDialogController.prototype.sendCancelAIManagement = function () {
        var cancelData = {
        // 可以根据需要添加其他参数
        };
        WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeCancelAIManagement, cancelData);
        console.log("已发送取消AI托管消息");
    };
    /**
     * 检查是否正在显示
     */
    AIManagedDialogController.prototype.isVisible = function () {
        return this.isShowing && this.node.active;
    };
    AIManagedDialogController.prototype.onDestroy = function () {
        // 清理事件监听
        if (this.maskNode) {
            this.maskNode.off(cc.Node.EventType.TOUCH_END, this.onMaskClick, this);
        }
        if (this.node) {
            this.node.off(cc.Node.EventType.TOUCH_END, this.onNodeClick, this);
        }
    };
    __decorate([
        property(cc.Node)
    ], AIManagedDialogController.prototype, "boardBg", void 0);
    __decorate([
        property(cc.Node)
    ], AIManagedDialogController.prototype, "maskNode", void 0);
    AIManagedDialogController = __decorate([
        ccclass
    ], AIManagedDialogController);
    return AIManagedDialogController;
}(cc.Component));
exports.default = AIManagedDialogController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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