
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/game/AIManagedDialogController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '423f58DLGJCFIMOAtc3Vt+Q', 'AIManagedDialogController');
// scripts/game/AIManagedDialogController.ts

"use strict";
// AI托管中页面控制器
// 当玩家进入AI托管状态时显示，点击屏幕任何位置发送取消托管消息
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var WebSocketManager_1 = require("../net/WebSocketManager");
var MessageId_1 = require("../net/MessageId");
var Config_1 = require("../util/Config");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var AIManagedDialogController = /** @class */ (function (_super) {
    __extends(AIManagedDialogController, _super);
    function AIManagedDialogController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.boardBg = null; // 背景板
        _this.maskNode = null; // 遮罩节点，用于接收点击事件
        _this.isShowing = false; // 是否正在显示
        return _this;
    }
    AIManagedDialogController.prototype.start = function () {
        console.log("AIManagedDialogController start() 被调用");
        // 初始化时隐藏
        this.node.active = false;
        // 检查节点配置
        console.log("检查节点配置:");
        console.log("- node:", this.node ? this.node.name : "null");
        console.log("- boardBg:", this.boardBg ? this.boardBg.name : "null");
        console.log("- maskNode:", this.maskNode ? this.maskNode.name : "null");
        // 如果 boardBg 没有设置，尝试查找子节点
        if (!this.boardBg) {
            console.log("boardBg 未设置，尝试查找子节点...");
            var bgNode = this.node.getChildByName('bot_bg');
            if (bgNode) {
                this.boardBg = bgNode;
                console.log("找到 bot_bg 子节点，设置为 boardBg");
            }
            else {
                console.warn("未找到 bot_bg 子节点");
            }
        }
        // 为遮罩节点添加点击事件监听
        if (this.maskNode) {
            // 检查是否有 BlockInputEvents 组件，如果有则禁用它
            var blockInputEvents = this.maskNode.getComponent(cc.BlockInputEvents);
            if (blockInputEvents) {
                console.log("发现 BlockInputEvents 组件，将其禁用以允许点击事件");
                blockInputEvents.enabled = false;
            }
            this.maskNode.on(cc.Node.EventType.TOUCH_END, this.onMaskClick, this);
            console.log("遮罩节点点击事件已设置，节点名称:", this.maskNode.name);
            console.log("遮罩节点大小:", this.maskNode.width, "x", this.maskNode.height);
            console.log("遮罩节点位置:", this.maskNode.position);
        }
        else {
            console.warn("maskNode 未设置");
        }
        // 同时为主节点添加点击事件监听作为备用
        this.node.on(cc.Node.EventType.TOUCH_END, this.onNodeClick, this);
    };
    /**
     * 显示托管中页面
     */
    AIManagedDialogController.prototype.show = function () {
        console.log("AIManagedDialogController.show() 被调用");
        if (this.isShowing) {
            console.log("托管页面已经在显示中，跳过");
            return;
        }
        console.log("开始显示托管页面...");
        this.isShowing = true;
        // 强制激活节点
        this.node.active = true;
        console.log("设置 node.active = true 后，实际值:", this.node.active);
        // 检查父节点是否激活
        var parent = this.node.parent;
        var parentChain = [];
        while (parent) {
            parentChain.push(parent.name + "(active: " + parent.active + ")");
            parent = parent.parent;
        }
        console.log("父节点链:", parentChain.join(" -> "));
        // 禁用 BlockInputEvents 组件以允许点击事件
        if (this.maskNode) {
            var blockInputEvents = this.maskNode.getComponent(cc.BlockInputEvents);
            if (blockInputEvents) {
                blockInputEvents.enabled = false;
                console.log("显示时禁用 BlockInputEvents 组件");
            }
        }
        // 强制刷新节点
        this.node.setPosition(this.node.position);
        console.log("托管页面节点已激活，最终状态:");
        console.log("- node.active:", this.node.active);
        console.log("- node.activeInHierarchy:", this.node.activeInHierarchy);
        console.log("- node.position:", this.node.position);
        console.log("- node.scale:", this.node.scale);
        console.log("- node.opacity:", this.node.opacity);
        // 初始化动画状态
        if (this.boardBg) {
            this.boardBg.scale = 0;
            this.boardBg.opacity = 0;
            console.log("背景板初始化完成");
        }
        else {
            console.warn("❌ boardBg 节点未设置");
        }
        // 执行显示动画
        this.playShowAnimation();
        console.log("托管页面显示动画已启动");
    };
    /**
     * 隐藏托管中页面
     */
    AIManagedDialogController.prototype.hide = function () {
        var _this = this;
        if (!this.isShowing) {
            return;
        }
        this.isShowing = false;
        // 重新启用 BlockInputEvents 组件（如果存在）
        if (this.maskNode) {
            var blockInputEvents = this.maskNode.getComponent(cc.BlockInputEvents);
            if (blockInputEvents) {
                blockInputEvents.enabled = true;
                console.log("重新启用 BlockInputEvents 组件");
            }
        }
        // 执行隐藏动画
        this.playHideAnimation(function () {
            _this.node.active = false;
        });
    };
    /**
     * 播放显示动画
     */
    AIManagedDialogController.prototype.playShowAnimation = function () {
        console.log("playShowAnimation 开始执行");
        if (!this.boardBg) {
            console.warn("❌ boardBg 为空，无法播放动画");
            return;
        }
        console.log("开始播放托管页面显示动画，动画时间:", Config_1.Config.dialogScaleTime);
        // 缩放和透明度动画
        cc.tween(this.boardBg)
            .to(Config_1.Config.dialogScaleTime, {
            scale: 1,
            opacity: 255
        }, {
            easing: 'backOut'
        })
            .call(function () {
            console.log("托管页面显示动画完成");
        })
            .start();
    };
    /**
     * 播放隐藏动画
     * @param callback 动画完成回调
     */
    AIManagedDialogController.prototype.playHideAnimation = function (callback) {
        if (!this.boardBg) {
            if (callback)
                callback();
            return;
        }
        // 缩放和透明度动画
        cc.tween(this.boardBg)
            .to(Config_1.Config.dialogScaleTime, {
            scale: 0,
            opacity: 0
        }, {
            easing: 'backIn'
        })
            .call(function () {
            if (callback)
                callback();
        })
            .start();
    };
    /**
     * 遮罩点击事件处理
     */
    AIManagedDialogController.prototype.onMaskClick = function () {
        console.log("遮罩节点被点击");
        this.handleClick();
    };
    /**
     * 主节点点击事件处理
     */
    AIManagedDialogController.prototype.onNodeClick = function () {
        console.log("主节点被点击");
        this.handleClick();
    };
    /**
     * 统一的点击处理逻辑
     */
    AIManagedDialogController.prototype.handleClick = function () {
        if (!this.isShowing) {
            console.log("托管页面未显示，忽略点击");
            return;
        }
        console.log("托管页面点击，发送取消AI托管消息");
        // 发送取消AI托管消息
        this.sendCancelAIManagement();
        // 立即隐藏页面（不等待服务器响应）
        this.hide();
    };
    /**
     * 发送取消AI托管消息
     */
    AIManagedDialogController.prototype.sendCancelAIManagement = function () {
        var cancelData = {
        // 可以根据需要添加其他参数
        };
        WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeCancelAIManagement, cancelData);
        console.log("已发送取消AI托管消息");
    };
    /**
     * 检查是否正在显示
     */
    AIManagedDialogController.prototype.isVisible = function () {
        return this.isShowing && this.node.active;
    };
    AIManagedDialogController.prototype.onDestroy = function () {
        // 清理事件监听
        if (this.maskNode) {
            this.maskNode.off(cc.Node.EventType.TOUCH_END, this.onMaskClick, this);
        }
        if (this.node) {
            this.node.off(cc.Node.EventType.TOUCH_END, this.onNodeClick, this);
        }
    };
    __decorate([
        property(cc.Node)
    ], AIManagedDialogController.prototype, "boardBg", void 0);
    __decorate([
        property(cc.Node)
    ], AIManagedDialogController.prototype, "maskNode", void 0);
    AIManagedDialogController = __decorate([
        ccclass
    ], AIManagedDialogController);
    return AIManagedDialogController;
}(cc.Component));
exports.default = AIManagedDialogController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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