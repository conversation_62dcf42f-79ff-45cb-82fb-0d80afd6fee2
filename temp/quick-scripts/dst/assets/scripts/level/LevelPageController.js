
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/level/LevelPageController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'c8e13uMxpxGELez69T2SfJx', 'LevelPageController');
// scripts/level/LevelPageController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var LeaveDialogController_1 = require("../hall/LeaveDialogController");
var Tools_1 = require("../util/Tools");
var Config_1 = require("../util/Config");
var GlobalManagerController_1 = require("../GlobalManagerController");
var SingleChessBoardController_1 = require("../game/Chess/SingleChessBoardController");
var WebSocketManager_1 = require("../net/WebSocketManager");
var MessageId_1 = require("../net/MessageId");
var GameMgr_1 = require("../common/GameMgr");
var EventCenter_1 = require("../common/EventCenter");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var LevelPageController = /** @class */ (function (_super) {
    __extends(LevelPageController, _super);
    function LevelPageController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        // 返回按钮
        _this.backButton = null;
        // 开始游戏按钮
        _this.startGameButton = null;
        // 地雷数UI标签
        _this.mineCountLabel = null;
        // 当前关卡数UI标签
        _this.currentLevelLabel = null;
        // 退出游戏弹窗
        _this.leaveDialogController = null;
        // level_page节点
        _this.levelPageNode = null;
        // game_map_1节点
        _this.gameMap1Node = null;
        // game_map_2节点
        _this.gameMap2Node = null;
        // 方形地图节点引用
        _this.qipan8x8Node = null; // level_page/game_map_1/chess_bg/qipan8*8
        _this.qipan8x9Node = null; // level_page/game_map_1/chess_bg/qipan8*9
        _this.qipan9x9Node = null; // level_page/game_map_1/chess_bg/qipan9*9
        _this.qipan9x10Node = null; // level_page/game_map_1/chess_bg/qipan9*10
        _this.qipan10x10Node = null; // level_page/game_map_1/chess_bg/qipan10*10
        // 特殊关卡节点引用
        _this.levelS001Node = null; // level_page/game_map_2/game_bg/Level_S001
        _this.levelS002Node = null; // level_page/game_map_2/game_bg/Level_S002
        _this.levelS003Node = null; // level_page/game_map_2/game_bg/Level_S003
        _this.levelS004Node = null; // level_page/game_map_2/game_bg/Level_S004
        _this.levelS005Node = null; // level_page/game_map_2/game_bg/Level_S005
        _this.levelS006Node = null; // level_page/game_map_2/game_bg/Level_S006
        // 单机模式棋盘控制器
        _this.singleChessBoardController = null;
        // 测试按钮（用于调试显示地雷位置）
        _this.debugShowMinesButton = null;
        // 测试预制体（用于显示地雷位置）
        _this.debugMinePrefab = null;
        // 存储创建的测试预制体节点，用于清理
        _this.debugMineNodes = [];
        // 结算页面相关节点
        _this.levelSettlementNode = null; // level_settlement节点
        _this.boardBgNode = null; // level_settlement/board_bg节点
        _this.loseBgNode = null; // level_settlement/board_bg/lose_bg节点
        _this.winBgNode = null; // level_settlement/board_bg/win_bg节点
        _this.retryButton = null; // 再来一次按钮
        _this.nextLevelButton = null; // 下一关按钮
        // 当前关卡数据
        _this.currentLevel = 1;
        _this.currentLevelInfo = null;
        _this.currentRoomId = 0; // 当前关卡游戏的房间ID
        _this.currentSingleChessBoard = null; // 当前激活的单机棋盘
        // 性能优化相关
        _this.lastShownMapNode = null; // 记录上次显示的地图节点
        _this.isUpdating = false; // 防止重复更新
        return _this;
    }
    LevelPageController.prototype.onLoad = function () {
        var _this = this;
        // 设置返回按钮点击事件 - 使用与GamePageController相同的样式
        if (this.backButton) {
            Tools_1.Tools.imageButtonClick(this.backButton, Config_1.Config.buttonRes + 'side_btn_back_normal', Config_1.Config.buttonRes + 'side_btn_back_pressed', function () {
                _this.onBackButtonClick();
            });
        }
        // 设置开始游戏按钮点击事件
        if (this.startGameButton) {
            this.startGameButton.node.on('click', this.onStartGameButtonClick, this);
        }
        // 注册单机模式消息监听
        this.registerSingleModeMessageHandlers();
        // 设置结算页面按钮事件
        this.setupSettlementButtons();
        // 注意：不在onLoad中初始化关卡数UI，等待外部调用setCurrentLevel时再更新
    };
    LevelPageController.prototype.start = function () {
        // 初始化时隐藏所有地图节点
        this.hideAllMapNodes();
        // 设置测试按钮点击事件
        this.setupDebugButton();
    };
    /**
     * 返回按钮点击事件
     */
    LevelPageController.prototype.onBackButtonClick = function () {
        // 如果没有有效的房间ID，说明还没有进入游戏，直接返回关卡选择页面
        if (this.currentRoomId <= 0) {
            this.returnToLevelSelect();
            return;
        }
        // 弹出确认退出对话框，type=1表示退出本局游戏，传递当前房间ID
        if (this.leaveDialogController) {
            this.leaveDialogController.show(1, function () {
            }, this.currentRoomId);
        }
        else {
            cc.warn("LeaveDialogController 未配置");
        }
    };
    /**
     * 返回到关卡选择页面
     */
    LevelPageController.prototype.returnToLevelSelect = function () {
        // 查找GlobalManagerController并切换到大厅页面
        var globalManagerNode = cc.find("Canvas/global_node") || cc.find("global_node");
        if (globalManagerNode) {
            var globalManager = globalManagerNode.getComponent(GlobalManagerController_1.default);
            if (globalManager) {
                globalManager.setCurrentPage(GlobalManagerController_1.PageType.HALL_PAGE);
            }
        }
    };
    /**
     * 开始游戏按钮点击事件
     */
    LevelPageController.prototype.onStartGameButtonClick = function () {
        // ExtendLevelInfo消息现在由LevelSelectPageController发送
        // 这里直接进入游戏，等待后端响应
    };
    /**
     * 处理ExtendLevelInfo响应
     * @param levelInfo 关卡信息响应数据
     */
    LevelPageController.prototype.onExtendLevelInfo = function (levelInfo) {
        cc.log("=== 收到ExtendLevelInfo响应 ===");
        this.currentLevelInfo = levelInfo;
        // 保存房间ID，用于退出时使用
        if (levelInfo.roomId) {
            this.currentRoomId = levelInfo.roomId;
        }
        // 重置关卡状态（包括清除测试预制体）
        this.resetLevelState();
        // 开始新游戏时，先重置当前棋盘（清理上一局的痕迹）
        if (this.currentSingleChessBoard) {
            this.currentSingleChessBoard.resetBoard();
        }
        // 更新地雷数UI
        this.updateMineCountUI(levelInfo.mineCount);
        // 使用当前设置的关卡编号，而不是后端返回的levelId
        // 因为后端的levelId可能与前端的关卡编号不一致
        this.enterLevel(this.currentLevel);
        // 通知当前激活的单机棋盘控制器处理ExtendLevelInfo
        if (this.currentSingleChessBoard) {
            this.currentSingleChessBoard.onExtendLevelInfo();
        }
    };
    /**
     * 更新地雷数UI
     * @param mineCount 地雷数量
     */
    LevelPageController.prototype.updateMineCountUI = function (mineCount) {
        if (this.mineCountLabel) {
            this.mineCountLabel.string = mineCount.toString();
        }
    };
    /**
     * 更新当前关卡数UI
     * @param levelNumber 关卡编号
     */
    LevelPageController.prototype.updateCurrentLevelUI = function (levelNumber) {
        if (this.currentLevelLabel) {
            this.currentLevelLabel.string = "\u7B2C" + levelNumber + "\u5173";
        }
    };
    /**
     * 根据关卡数进入相应的关卡（优化版本）
     * @param levelNumber 关卡编号
     */
    LevelPageController.prototype.enterLevel = function (levelNumber) {
        // 防止重复更新
        if (this.isUpdating) {
            return;
        }
        this.isUpdating = true;
        // 更新关卡数UI显示
        this.updateCurrentLevelUI(levelNumber);
        // 获取目标地图节点和容器
        var targetMapInfo = this.getMapNodeByLevel(levelNumber);
        if (!targetMapInfo) {
            cc.warn("\u672A\u77E5\u7684\u5173\u5361\u7F16\u53F7: " + levelNumber);
            this.isUpdating = false;
            return;
        }
        // 只有当目标节点与当前显示的节点不同时才进行切换
        if (this.lastShownMapNode !== targetMapInfo.mapNode) {
            // 隐藏上一个显示的地图节点
            if (this.lastShownMapNode) {
                this.lastShownMapNode.active = false;
            }
            // 显示目标容器和地图节点
            this.showMapContainer(targetMapInfo.containerType);
            this.showMapNodeOptimized(targetMapInfo.mapNode, targetMapInfo.mapName);
            // 记录当前显示的节点
            this.lastShownMapNode = targetMapInfo.mapNode;
        }
        // 设置当前激活的单机棋盘控制器
        if (this.singleChessBoardController) {
            // 根据关卡设置棋盘类型
            var boardType = this.getBoardTypeByLevel(levelNumber);
            this.singleChessBoardController.initBoard(boardType);
            this.currentSingleChessBoard = this.singleChessBoardController;
        }
        else {
            this.currentSingleChessBoard = null;
        }
        this.isUpdating = false;
    };
    /**
     * 根据关卡数获取对应的地图节点信息
     * @param levelNumber 关卡编号
     */
    LevelPageController.prototype.getMapNodeByLevel = function (levelNumber) {
        if (levelNumber >= 1 && levelNumber <= 4) {
            return { mapNode: this.qipan8x8Node, mapName: "qipan8*8", containerType: 'map1' };
        }
        else if (levelNumber === 5) {
            return { mapNode: this.levelS001Node, mapName: "Level_S001", containerType: 'map2' };
        }
        else if (levelNumber >= 6 && levelNumber <= 9) {
            return { mapNode: this.qipan8x9Node, mapName: "qipan8*9", containerType: 'map1' };
        }
        else if (levelNumber === 10) {
            return { mapNode: this.levelS002Node, mapName: "Level_S002", containerType: 'map2' };
        }
        else if (levelNumber >= 11 && levelNumber <= 14) {
            return { mapNode: this.qipan9x9Node, mapName: "qipan9*9", containerType: 'map1' };
        }
        else if (levelNumber === 15) {
            return { mapNode: this.levelS003Node, mapName: "Level_S003", containerType: 'map2' };
        }
        else if (levelNumber >= 16 && levelNumber <= 19) {
            return { mapNode: this.qipan9x10Node, mapName: "qipan9*10", containerType: 'map1' };
        }
        else if (levelNumber === 20) {
            return { mapNode: this.levelS004Node, mapName: "Level_S004", containerType: 'map2' };
        }
        else if (levelNumber >= 21 && levelNumber <= 24) {
            return { mapNode: this.qipan10x10Node, mapName: "qipan10*10", containerType: 'map1' };
        }
        else if (levelNumber === 25) {
            return { mapNode: this.levelS005Node, mapName: "Level_S005", containerType: 'map2' };
        }
        else if (levelNumber >= 26 && levelNumber <= 29) {
            return { mapNode: this.qipan10x10Node, mapName: "qipan10*10", containerType: 'map1' };
        }
        else if (levelNumber === 30) {
            return { mapNode: this.levelS006Node, mapName: "Level_S006", containerType: 'map2' };
        }
        return null;
    };
    /**
     * 根据关卡编号获取棋盘类型
     * @param levelNumber 关卡编号
     */
    LevelPageController.prototype.getBoardTypeByLevel = function (levelNumber) {
        if (levelNumber >= 1 && levelNumber <= 4) {
            return "8x8";
        }
        else if (levelNumber >= 6 && levelNumber <= 9) {
            return "8x9";
        }
        else if (levelNumber >= 11 && levelNumber <= 14) {
            return "9x9";
        }
        else if (levelNumber >= 16 && levelNumber <= 19) {
            return "9x10";
        }
        else if (levelNumber >= 21 && levelNumber <= 24 || levelNumber >= 26 && levelNumber <= 29) {
            return "10x10";
        }
        return "8x8"; // 默认
    };
    /**
     * 显示指定的地图节点（优化版本）
     * @param mapNode 要显示的地图节点
     * @param mapName 地图名称（用于日志）
     */
    LevelPageController.prototype.showMapNodeOptimized = function (mapNode, mapName) {
        if (mapNode) {
            mapNode.active = true;
        }
        else {
            cc.warn("\u274C \u5730\u56FE\u8282\u70B9\u672A\u627E\u5230: " + mapName);
        }
    };
    /**
     * 显示指定的地图容器
     * @param containerType 容器类型
     */
    LevelPageController.prototype.showMapContainer = function (containerType) {
        // 确保 level_page 节点是激活的
        if (this.levelPageNode) {
            this.levelPageNode.active = true;
        }
        // 根据容器类型显示对应容器，隐藏另一个
        if (containerType === 'map1') {
            if (this.gameMap1Node && !this.gameMap1Node.active) {
                this.gameMap1Node.active = true;
            }
            if (this.gameMap2Node && this.gameMap2Node.active) {
                this.gameMap2Node.active = false;
            }
        }
        else {
            if (this.gameMap2Node && !this.gameMap2Node.active) {
                this.gameMap2Node.active = true;
            }
            if (this.gameMap1Node && this.gameMap1Node.active) {
                this.gameMap1Node.active = false;
            }
        }
    };
    /**
     * 隐藏所有地图节点
     */
    LevelPageController.prototype.hideAllMapNodes = function () {
        var allMapNodes = [
            this.qipan8x8Node,
            this.qipan8x9Node,
            this.qipan9x9Node,
            this.qipan9x10Node,
            this.qipan10x10Node,
            this.levelS001Node,
            this.levelS002Node,
            this.levelS003Node,
            this.levelS004Node,
            this.levelS005Node,
            this.levelS006Node
        ];
        allMapNodes.forEach(function (node) {
            if (node) {
                node.active = false;
            }
        });
    };
    /**
     * 设置当前关卡（从外部调用）
     * @param levelNumber 关卡编号
     */
    LevelPageController.prototype.setCurrentLevel = function (levelNumber) {
        cc.log("=== \u8BBE\u7F6E\u5F53\u524D\u5173\u5361: " + levelNumber + " ===");
        // 重置关卡状态（包括清除测试预制体）
        this.resetLevelState();
        this.currentLevel = levelNumber;
        // 立即根据关卡数切换地图显示
        this.enterLevel(levelNumber);
    };
    /**
     * 获取当前关卡编号
     */
    LevelPageController.prototype.getCurrentLevel = function () {
        return this.currentLevel;
    };
    /**
     * 获取当前关卡信息
     */
    LevelPageController.prototype.getCurrentLevelInfo = function () {
        return this.currentLevelInfo;
    };
    /**
     * 获取当前房间ID
     */
    LevelPageController.prototype.getCurrentRoomId = function () {
        return this.currentRoomId;
    };
    /**
     * 隐藏所有地图容器
     */
    LevelPageController.prototype.hideAllMapContainers = function () {
        // 确保 level_page 节点是激活的
        if (this.levelPageNode) {
            this.levelPageNode.active = true;
        }
        // 隐藏两个主要的地图容器
        if (this.gameMap1Node) {
            this.gameMap1Node.active = false;
        }
        if (this.gameMap2Node) {
            this.gameMap2Node.active = false;
        }
        // 同时隐藏所有具体的地图节点
        this.hideAllMapNodes();
    };
    /**
     * 显示 game_map_1 容器（方形地图）
     */
    LevelPageController.prototype.showGameMap1 = function () {
        if (this.gameMap1Node) {
            this.gameMap1Node.active = true;
        }
        else {
            cc.warn("❌ game_map_1 节点未找到");
        }
    };
    /**
     * 显示 game_map_2 容器（特殊关卡）
     */
    LevelPageController.prototype.showGameMap2 = function () {
        if (this.gameMap2Node) {
            this.gameMap2Node.active = true;
        }
        else {
            cc.warn("❌ game_map_2 节点未找到");
        }
    };
    /**
     * 获取当前激活的单机棋盘控制器
     */
    LevelPageController.prototype.getCurrentSingleChessBoard = function () {
        return this.currentSingleChessBoard;
    };
    /**
     * 处理单机模式的点击响应
     * @param response 点击响应数据
     */
    LevelPageController.prototype.handleSingleModeClickResponse = function (response) {
        if (this.currentSingleChessBoard) {
            var x = response.x, y = response.y, result = response.result, chainReaction = response.chainReaction;
            // 处理点击结果
            if (x !== undefined && y !== undefined && result !== undefined) {
                this.currentSingleChessBoard.handleClickResponse(x, y, result);
            }
            // 处理连锁反应
            if (chainReaction && Array.isArray(chainReaction)) {
                this.currentSingleChessBoard.handleChainReaction(chainReaction);
            }
        }
    };
    /**
     * 处理单机模式游戏结束
     * @param gameEndData 游戏结束数据
     */
    LevelPageController.prototype.handleSingleModeGameEnd = function (gameEndData) {
        if (this.currentSingleChessBoard) {
            // 禁用棋盘触摸
            this.currentSingleChessBoard.disableAllGridTouch();
            // 处理游戏结束
            this.currentSingleChessBoard.onLevelGameEnd();
        }
    };
    /**
     * 重置当前单机棋盘（仅在开始新游戏时调用）
     */
    LevelPageController.prototype.resetCurrentSingleChessBoard = function () {
        if (this.currentSingleChessBoard) {
            // 重置棋盘状态（清理所有预制体和格子状态）
            this.currentSingleChessBoard.resetBoard();
            // 重新启用触摸事件
            this.currentSingleChessBoard.enableAllGridTouch();
        }
    };
    /**
     * 注册单机模式消息处理器
     */
    LevelPageController.prototype.registerSingleModeMessageHandlers = function () {
        // 监听WebSocket消息
        GameMgr_1.GameMgr.Event.AddEventListener(EventCenter_1.EventType.ReceiveMessage, this.onReceiveMessage, this);
    };
    /**
     * 取消单机模式消息监听
     */
    LevelPageController.prototype.unregisterSingleModeMessageHandlers = function () {
        GameMgr_1.GameMgr.Event.RemoveEventListener(EventCenter_1.EventType.ReceiveMessage, this.onReceiveMessage, this);
    };
    /**
     * 处理接收到的WebSocket消息
     * @param messageBean 消息数据
     */
    LevelPageController.prototype.onReceiveMessage = function (messageBean) {
        switch (messageBean.msgId) {
            case MessageId_1.MessageId.MsgTypeLevelClickBlock:
                this.onLevelClickBlockResponse(messageBean.data);
                break;
            case MessageId_1.MessageId.MsgTypeLevelGameEnd:
                this.onLevelGameEnd(messageBean.data);
                break;
        }
    };
    /**
     * 处理LevelClickBlock响应
     * @param response 点击响应数据
     */
    LevelPageController.prototype.onLevelClickBlockResponse = function (response) {
        if (this.currentSingleChessBoard) {
            // 解构响应数据，支持多种可能的字段名
            var x = response.x, y = response.y, result = response.result, action = response.action, chainReaction = response.chainReaction, revealedGrids = response.revealedGrids, floodFill = response.floodFill, revealedBlocks = response.revealedBlocks, floodFillResults = response.floodFillResults // 单机模式使用这个字段
            ;
            // 根据action类型处理不同的响应
            if (x !== undefined && y !== undefined && result !== undefined) {
                if (action === 2) {
                    // 标记/取消标记操作，不调用handleClickResponse，避免格子消失
                    // 不调用 handleClickResponse，因为标记操作不应该隐藏格子
                }
                else if (action === 1) {
                    // 挖掘操作
                    this.currentSingleChessBoard.handleClickResponse(x, y, result);
                    // 处理连锁展开数据
                    if (floodFillResults && Array.isArray(floodFillResults) && floodFillResults.length > 0) {
                        this.currentSingleChessBoard.handleFloodFillResults(floodFillResults);
                    }
                }
                else {
                    // 其他操作，默认按挖掘处理
                    this.currentSingleChessBoard.handleClickResponse(x, y, result);
                }
            }
        }
    };
    /**
     * 处理LevelGameEnd通知
     * @param gameEndData 游戏结束数据
     */
    LevelPageController.prototype.onLevelGameEnd = function (gameEndData) {
        if (this.currentSingleChessBoard) {
            // 禁用棋盘触摸
            this.currentSingleChessBoard.disableAllGridTouch();
            // 处理游戏结束（不清理数据）
            this.currentSingleChessBoard.onLevelGameEnd();
        }
        // 显示结算页面
        this.showLevelSettlement(gameEndData);
    };
    /**
     * 设置结算页面按钮事件
     */
    LevelPageController.prototype.setupSettlementButtons = function () {
        // 再来一次按钮
        if (this.retryButton) {
            this.retryButton.node.on('click', this.onRetryButtonClick, this);
        }
        // 下一关按钮
        if (this.nextLevelButton) {
            this.nextLevelButton.node.on('click', this.onNextLevelButtonClick, this);
        }
    };
    /**
     * 显示结算页面
     * @param gameEndData 游戏结束数据
     */
    LevelPageController.prototype.showLevelSettlement = function (gameEndData) {
        if (!this.levelSettlementNode) {
            console.error("levelSettlementNode 未配置");
            return;
        }
        // 显示结算页面
        this.levelSettlementNode.active = true;
        // 根据游戏结果显示对应的背景 - 修复成功判断逻辑
        var isSuccess = gameEndData.isSuccess || gameEndData.success || gameEndData.isWin || gameEndData.gameStatus === 1;
        if (isSuccess) {
            // 成功 - 显示胜利背景
            if (this.winBgNode) {
                this.winBgNode.active = true;
            }
            if (this.loseBgNode) {
                this.loseBgNode.active = false;
            }
        }
        else {
            // 失败 - 显示失败背景
            if (this.loseBgNode) {
                this.loseBgNode.active = true;
            }
            if (this.winBgNode) {
                this.winBgNode.active = false;
            }
        }
    };
    /**
     * 再来一次按钮点击事件
     */
    LevelPageController.prototype.onRetryButtonClick = function () {
        // 关闭结算页面
        this.hideLevelSettlement();
        // 注意：不在这里重置棋盘，等收到ExtendLevelInfo响应时再重置
        // 这样可以避免过早清理，让玩家在点击按钮后还能短暂看到游玩痕迹
        // 发送当前关卡的ExtendLevelInfo
        this.sendExtendLevelInfo(this.currentLevel);
    };
    /**
     * 下一关按钮点击事件
     */
    LevelPageController.prototype.onNextLevelButtonClick = function () {
        // 关闭结算页面
        this.hideLevelSettlement();
        // 进入下一关
        var nextLevel = this.currentLevel + 1;
        this.setCurrentLevel(nextLevel);
        // 注意：不在这里重置棋盘，等收到ExtendLevelInfo响应时再重置
        // 这样可以避免过早清理，让玩家在点击按钮后还能短暂看到游玩痕迹
        // 发送下一关的ExtendLevelInfo
        this.sendExtendLevelInfo(nextLevel);
    };
    /**
     * 隐藏结算页面
     */
    LevelPageController.prototype.hideLevelSettlement = function () {
        if (this.levelSettlementNode) {
            this.levelSettlementNode.active = false;
        }
    };
    /**
     * 发送ExtendLevelInfo消息
     * @param levelId 关卡ID
     */
    LevelPageController.prototype.sendExtendLevelInfo = function (levelId) {
        var request = {
            levelId: levelId
        };
        WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeExtendLevelInfo, request);
    };
    /**
     * 设置测试按钮
     */
    LevelPageController.prototype.setupDebugButton = function () {
        if (this.debugShowMinesButton) {
            this.debugShowMinesButton.node.on('click', this.onDebugShowMinesClick, this);
        }
    };
    /**
     * 测试按钮点击事件 - 发送DebugShowMines消息
     */
    LevelPageController.prototype.onDebugShowMinesClick = function () {
        cc.log("=== 测试按钮被点击 ===");
        cc.log("发送DebugShowMines消息");
        WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeDebugShowMines, {});
    };
    /**
     * 判断是否在单机模式
     */
    LevelPageController.prototype.isInSingleMode = function () {
        // 单机模式的判断逻辑：当前页面是关卡页面且有有效的房间ID
        return this.currentRoomId > 0;
    };
    /**
     * 处理DebugShowMines响应，在炸弹位置生成测试预制体
     * @param minePositions 炸弹位置数组 [{x: number, y: number}]
     */
    LevelPageController.prototype.handleDebugShowMines = function (minePositions) {
        var _this = this;
        cc.log("=== handleDebugShowMines 被调用 ===");
        cc.log("接收到的地雷位置数据:", minePositions);
        cc.log("debugMinePrefab 是否存在:", !!this.debugMinePrefab);
        cc.log("currentSingleChessBoard 是否存在:", !!this.currentSingleChessBoard);
        if (!this.debugMinePrefab) {
            cc.warn("debugMinePrefab 预制体未设置，无法显示测试标记");
            return;
        }
        if (!this.currentSingleChessBoard) {
            cc.warn("当前没有激活的单机棋盘");
            return;
        }
        if (!minePositions || !Array.isArray(minePositions) || minePositions.length === 0) {
            cc.warn("地雷位置数据无效:", minePositions);
            return;
        }
        cc.log("收到地雷位置数据，开始生成测试预制体:", minePositions);
        cc.log("地雷位置数组长度:", minePositions.length);
        // 先尝试直接创建一个测试预制体，不使用延迟
        cc.log("=== 尝试直接创建第一个测试预制体 ===");
        if (minePositions.length > 0) {
            var firstPosition = minePositions[0];
            cc.log("第一个位置:", firstPosition);
            cc.log("第一个位置的x:", firstPosition.x, "类型:", typeof firstPosition.x);
            cc.log("第一个位置的y:", firstPosition.y, "类型:", typeof firstPosition.y);
            // 直接调用，不使用延迟
            this.createDebugMinePrefab(firstPosition.x, firstPosition.y);
        }
        // 在每个炸弹位置生成测试预制体
        minePositions.forEach(function (position, index) {
            cc.log("\u51C6\u5907\u5728\u4F4D\u7F6E (" + position.x + ", " + position.y + ") \u751F\u6210\u6D4B\u8BD5\u9884\u5236\u4F53\uFF0C\u5EF6\u8FDF " + index * 0.1 + " \u79D2");
            cc.log("position \u5BF9\u8C61:", position);
            cc.log("position.x:", position.x, "position.y:", position.y);
            if (index === 0) {
                // 第一个不延迟，立即执行
                cc.log("\u7ACB\u5373\u521B\u5EFA\u7B2C\u4E00\u4E2A\u6D4B\u8BD5\u9884\u5236\u4F53");
                _this.createDebugMinePrefab(position.x, position.y);
            }
            else {
                // 其他的使用延迟
                _this.scheduleOnce(function () {
                    cc.log("\u5EF6\u8FDF\u521B\u5EFA\u6D4B\u8BD5\u9884\u5236\u4F53\uFF0C\u4F4D\u7F6E: (" + position.x + ", " + position.y + ")");
                    _this.createDebugMinePrefab(position.x, position.y);
                }, index * 0.1);
            }
        });
    };
    /**
     * 在指定位置创建测试预制体
     * @param x 格子x坐标
     * @param y 格子y坐标
     */
    LevelPageController.prototype.createDebugMinePrefab = function (x, y) {
        cc.log("=== createDebugMinePrefab \u88AB\u8C03\u7528\uFF0C\u4F4D\u7F6E: (" + x + ", " + y + ") ===");
        if (!this.debugMinePrefab) {
            cc.error("debugMinePrefab 为空，无法创建测试预制体");
            return;
        }
        if (!this.currentSingleChessBoard) {
            cc.error("currentSingleChessBoard 为空，无法创建测试预制体");
            return;
        }
        cc.log("开始调用 createCustomPrefab 方法");
        try {
            // 使用棋盘控制器的公共方法创建自定义预制体
            var debugNode = this.currentSingleChessBoard.createCustomPrefab(x, y, this.debugMinePrefab, "DebugMine_" + x + "_" + y);
            if (debugNode) {
                // 将创建的节点存储起来，用于后续清理
                this.debugMineNodes.push(debugNode);
                cc.log("\u2705 \u6210\u529F\u5728\u4F4D\u7F6E (" + x + ", " + y + ") \u521B\u5EFA\u4E86\u6D4B\u8BD5\u9884\u5236\u4F53:", debugNode.name);
                cc.log("\u5F53\u524D\u6D4B\u8BD5\u9884\u5236\u4F53\u6570\u91CF:", this.debugMineNodes.length);
            }
            else {
                cc.error("\u274C \u5728\u4F4D\u7F6E (" + x + ", " + y + ") \u521B\u5EFA\u6D4B\u8BD5\u9884\u5236\u4F53\u5931\u8D25\uFF0C\u8FD4\u56DE\u503C\u4E3A\u7A7A");
            }
        }
        catch (error) {
            cc.error("\u274C \u521B\u5EFA\u6D4B\u8BD5\u9884\u5236\u4F53\u65F6\u53D1\u751F\u9519\u8BEF:", error);
        }
    };
    /**
     * 清除所有测试预制体
     */
    LevelPageController.prototype.clearDebugMines = function () {
        cc.log("=== 清除测试预制体 ===");
        cc.log("当前测试预制体数量:", this.debugMineNodes.length);
        this.debugMineNodes.forEach(function (node, index) {
            if (node && cc.isValid(node)) {
                cc.log("\u6E05\u9664\u6D4B\u8BD5\u9884\u5236\u4F53 " + (index + 1) + ":", node.name);
                node.destroy();
            }
        });
        // 清空数组
        this.debugMineNodes = [];
        cc.log("测试预制体清除完成");
    };
    /**
     * 重置关卡状态（包括清除测试预制体）
     */
    LevelPageController.prototype.resetLevelState = function () {
        cc.log("=== 重置关卡状态 ===");
        this.clearDebugMines();
        // 这里可以添加其他需要重置的状态
    };
    LevelPageController.prototype.onDestroy = function () {
        // 取消消息监听
        this.unregisterSingleModeMessageHandlers();
        // 清理测试预制体
        this.clearDebugMines();
        // 清理按钮事件
        if (this.retryButton) {
            this.retryButton.node.off('click', this.onRetryButtonClick, this);
        }
        if (this.nextLevelButton) {
            this.nextLevelButton.node.off('click', this.onNextLevelButtonClick, this);
        }
        if (this.debugShowMinesButton) {
            this.debugShowMinesButton.node.off('click', this.onDebugShowMinesClick, this);
        }
    };
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "backButton", void 0);
    __decorate([
        property(cc.Button)
    ], LevelPageController.prototype, "startGameButton", void 0);
    __decorate([
        property(cc.Label)
    ], LevelPageController.prototype, "mineCountLabel", void 0);
    __decorate([
        property(cc.Label)
    ], LevelPageController.prototype, "currentLevelLabel", void 0);
    __decorate([
        property(LeaveDialogController_1.default)
    ], LevelPageController.prototype, "leaveDialogController", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelPageNode", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "gameMap1Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "gameMap2Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan8x8Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan8x9Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan9x9Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan9x10Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan10x10Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS001Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS002Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS003Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS004Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS005Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS006Node", void 0);
    __decorate([
        property(SingleChessBoardController_1.default)
    ], LevelPageController.prototype, "singleChessBoardController", void 0);
    __decorate([
        property(cc.Button)
    ], LevelPageController.prototype, "debugShowMinesButton", void 0);
    __decorate([
        property(cc.Prefab)
    ], LevelPageController.prototype, "debugMinePrefab", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelSettlementNode", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "boardBgNode", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "loseBgNode", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "winBgNode", void 0);
    __decorate([
        property(cc.Button)
    ], LevelPageController.prototype, "retryButton", void 0);
    __decorate([
        property(cc.Button)
    ], LevelPageController.prototype, "nextLevelButton", void 0);
    LevelPageController = __decorate([
        ccclass
    ], LevelPageController);
    return LevelPageController;
}(cc.Component));
exports.default = LevelPageController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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