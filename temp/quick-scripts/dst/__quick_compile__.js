
(function () {
var scripts = [{"deps":{"./joui18n-script/LocalizedSprite":1,"./joui18n-script/LocalizedLabel":2,"./assets/meshTools/Singleton":8,"./assets/meshTools/BaseSDK":16,"./assets/meshTools/tools/MeshSdkApi":81,"./assets/meshTools/tools/Publish":22,"./assets/meshTools/tools/MeshSdk":3,"./assets/scripts/TipsDialogController":25,"./assets/scripts/ToastController":39,"./assets/scripts/GlobalManagerController":18,"./assets/scripts/bean/GameBean":17,"./assets/scripts/bean/GlobalBean":4,"./assets/scripts/bean/LanguageType":20,"./assets/scripts/bean/EnumBean":19,"./assets/scripts/common/GameData":9,"./assets/scripts/common/GameMgr":21,"./assets/scripts/common/GameTools":23,"./assets/scripts/common/MineConsole":24,"./assets/scripts/common/EventCenter":53,"./assets/scripts/game/BtnController":30,"./assets/scripts/game/CongratsDialogController":26,"./assets/scripts/game/GamePageController":29,"./assets/scripts/game/GameScoreController":28,"./assets/scripts/game/AIManagedDialogController":27,"./assets/scripts/game/Chess/GridController":5,"./assets/scripts/game/Chess/HexChessBoardController":31,"./assets/scripts/game/Chess/SingleChessBoardController":63,"./assets/scripts/game/Chess/ChessBoardController":35,"./assets/scripts/hall/HallCenterLayController":33,"./assets/scripts/hall/HallCreateRoomController":32,"./assets/scripts/hall/HallJoinRoomController":36,"./assets/scripts/hall/HallPageController":34,"./assets/scripts/hall/HallParentController":37,"./assets/scripts/hall/InfoDialogController":38,"./assets/scripts/hall/KickOutDialogController":40,"./assets/scripts/hall/LeaveDialogController":42,"./assets/scripts/hall/LevelSelectDemo":78,"./assets/scripts/hall/MatchParentController":41,"./assets/scripts/hall/PlayerLayoutController":43,"./assets/scripts/hall/SettingDialogController":44,"./assets/scripts/hall/TopUpDialogController":51,"./assets/scripts/hall/HallAutoController":45,"./assets/scripts/hall/Level/LevelSelectController":6,"./assets/scripts/hall/Level/LevelSelectExample":47,"./assets/scripts/hall/Level/LevelSelectPageController":46,"./assets/scripts/hall/Level/ScrollViewHelper":52,"./assets/scripts/hall/Level/LevelItemController":48,"./assets/scripts/level/LevelPageController":10,"./assets/scripts/net/GameServerUrl":11,"./assets/scripts/net/HttpManager":49,"./assets/scripts/net/HttpUtils":50,"./assets/scripts/net/IHttpMsgBody":66,"./assets/scripts/net/MessageBaseBean":54,"./assets/scripts/net/MessageId":55,"./assets/scripts/net/WebSocketManager":56,"./assets/scripts/net/WebSocketTool":57,"./assets/scripts/net/ErrorCode":67,"./assets/scripts/pfb/InfoItemController":12,"./assets/scripts/pfb/InfoItemOneController":58,"./assets/scripts/pfb/MatchItemController":59,"./assets/scripts/pfb/PlayerGameController ":60,"./assets/scripts/pfb/PlayerScoreController":61,"./assets/scripts/pfb/SeatItemController":68,"./assets/scripts/pfb/CongratsItemController":62,"./assets/scripts/start_up/StartUpPageController":13,"./assets/scripts/start_up/StartUpCenterController":64,"./assets/scripts/test/DebugShowMinesTest":14,"./assets/scripts/test/NoticeRoundStartTest":72,"./assets/scripts/test/AnimationTest":65,"./assets/scripts/util/AudioMgr":15,"./assets/scripts/util/BlockingQueue":76,"./assets/scripts/util/Config":80,"./assets/scripts/util/Dictionary":71,"./assets/scripts/util/LocalStorageManager":69,"./assets/scripts/util/NickNameLabel":70,"./assets/scripts/util/Tools":77,"./assets/scripts/util/AudioManager":79,"./assets/meshTools/MeshTools":73,"./assets/resources/i18n/zh_HK":7,"./assets/resources/i18n/en":75,"./assets/resources/i18n/zh_CN":74},"path":"preview-scripts/__qc_index__.js"},{"deps":{},"path":"preview-scripts/joui18n-script/LocalizedSprite.js"},{"deps":{},"path":"preview-scripts/joui18n-script/LocalizedLabel.js"},{"deps":{},"path":"preview-scripts/assets/meshTools/tools/MeshSdk.js"},{"deps":{"../../meshTools/Singleton":8,"../hall/HallAutoController":45},"path":"preview-scripts/assets/scripts/bean/GlobalBean.js"},{"deps":{},"path":"preview-scripts/assets/scripts/game/Chess/GridController.js"},{"deps":{"./ScrollViewHelper":52},"path":"preview-scripts/assets/scripts/hall/Level/LevelSelectController.js"},{"deps":{},"path":"preview-scripts/assets/resources/i18n/zh_HK.js"},{"deps":{},"path":"preview-scripts/assets/meshTools/Singleton.js"},{"deps":{"../../meshTools/MeshTools":73,"../../meshTools/Singleton":8,"../net/GameServerUrl":11},"path":"preview-scripts/assets/scripts/common/GameData.js"},{"deps":{"../hall/LeaveDialogController":42,"../util/Tools":77,"../util/Config":80,"../GlobalManagerController":18,"../game/Chess/SingleChessBoardController":63,"../net/WebSocketManager":56,"../net/MessageId":55,"../common/GameMgr":21,"../common/EventCenter":53},"path":"preview-scripts/assets/scripts/level/LevelPageController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/GameServerUrl.js"},{"deps":{},"path":"preview-scripts/assets/scripts/pfb/InfoItemController.js"},{"deps":{"../common/GameMgr":21,"./StartUpCenterController":64},"path":"preview-scripts/assets/scripts/start_up/StartUpPageController.js"},{"deps":{"../net/MessageId":55,"../net/WebSocketManager":56,"../common/GameMgr":21,"../common/EventCenter":53},"path":"preview-scripts/assets/scripts/test/DebugShowMinesTest.js"},{"deps":{"./Config":80,"./Dictionary":71},"path":"preview-scripts/assets/scripts/util/AudioMgr.js"},{"deps":{},"path":"preview-scripts/assets/meshTools/BaseSDK.js"},{"deps":{},"path":"preview-scripts/assets/scripts/bean/GameBean.js"},{"deps":{"../meshTools/MeshTools":73,"../meshTools/tools/Publish":22,"./bean/EnumBean":19,"./bean/GlobalBean":4,"./bean/LanguageType":20,"./common/EventCenter":53,"./common/GameMgr":21,"./game/GamePageController":29,"./hall/HallPageController":34,"./level/LevelPageController":10,"./hall/TopUpDialogController":51,"./net/ErrorCode":67,"./net/GameServerUrl":11,"./net/MessageBaseBean":54,"./net/MessageId":55,"./net/WebSocketManager":56,"./net/WebSocketTool":57,"./start_up/StartUpPageController":13,"./TipsDialogController":25,"./ToastController":39,"./util/AudioMgr":15,"./util/Config":80},"path":"preview-scripts/assets/scripts/GlobalManagerController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/bean/EnumBean.js"},{"deps":{},"path":"preview-scripts/assets/scripts/bean/LanguageType.js"},{"deps":{"../../meshTools/tools/MeshSdkApi":81,"./EventCenter":53,"./GameData":9,"./GameTools":23,"./MineConsole":24},"path":"preview-scripts/assets/scripts/common/GameMgr.js"},{"deps":{"../Singleton":8},"path":"preview-scripts/assets/meshTools/tools/Publish.js"},{"deps":{"../../meshTools/Singleton":8},"path":"preview-scripts/assets/scripts/common/GameTools.js"},{"deps":{"../../meshTools/Singleton":8},"path":"preview-scripts/assets/scripts/common/MineConsole.js"},{"deps":{"./util/Config":80,"./util/Tools":77},"path":"preview-scripts/assets/scripts/TipsDialogController.js"},{"deps":{"../bean/GlobalBean":4,"../common/EventCenter":53,"../common/GameMgr":21,"../net/MessageBaseBean":54,"../pfb/CongratsItemController":62,"../util/Config":80,"../util/Tools":77},"path":"preview-scripts/assets/scripts/game/CongratsDialogController.js"},{"deps":{"../net/WebSocketManager":56,"../net/MessageId":55,"../util/Config":80},"path":"preview-scripts/assets/scripts/game/AIManagedDialogController.js"},{"deps":{"../bean/GlobalBean":4,"../pfb/PlayerScoreController":61},"path":"preview-scripts/assets/scripts/game/GameScoreController.js"},{"deps":{"./CongratsDialogController":26,"./GameScoreController":28,"./AIManagedDialogController":27,"../bean/GlobalBean":4,"../hall/LeaveDialogController":42,"../util/Config":80,"../util/Tools":77,"../util/AudioManager":79,"./Chess/HexChessBoardController":31,"./Chess/ChessBoardController":35,"../pfb/PlayerGameController ":60,"../net/MessageId":55,"../net/WebSocketManager":56},"path":"preview-scripts/assets/scripts/game/GamePageController.js"},{"deps":{"../util/AudioManager":79,"../util/Config":80,"../util/LocalStorageManager":69},"path":"preview-scripts/assets/scripts/game/BtnController.js"},{"deps":{"../../bean/GlobalBean":4,"../../pfb/PlayerGameController ":60},"path":"preview-scripts/assets/scripts/game/Chess/HexChessBoardController.js"},{"deps":{"../bean/GlobalBean":4,"../pfb/SeatItemController":68,"../util/Config":80,"../util/Tools":77},"path":"preview-scripts/assets/scripts/hall/HallCreateRoomController.js"},{"deps":{"../bean/GlobalBean":4,"../net/MessageId":55,"../net/WebSocketManager":56,"../ToastController":39,"./HallAutoController":45,"./HallCreateRoomController":32,"./HallJoinRoomController":36},"path":"preview-scripts/assets/scripts/hall/HallCenterLayController.js"},{"deps":{"../bean/GlobalBean":4,"../common/GameMgr":21,"../net/MessageId":55,"../net/WebSocketManager":56,"../net/WebSocketTool":57,"../ToastController":39,"../util/AudioManager":79,"./HallParentController":37,"./InfoDialogController":38,"./KickOutDialogController":40,"./LeaveDialogController":42,"./Level/LevelSelectPageController":46,"./MatchParentController":41,"./SettingDialogController":44},"path":"preview-scripts/assets/scripts/hall/HallPageController.js"},{"deps":{"../../bean/GlobalBean":4,"../../pfb/PlayerGameController ":60},"path":"preview-scripts/assets/scripts/game/Chess/ChessBoardController.js"},{"deps":{"../util/Tools":77},"path":"preview-scripts/assets/scripts/hall/HallJoinRoomController.js"},{"deps":{"../../meshTools/tools/Publish":22,"../bean/GlobalBean":4,"../common/GameMgr":21,"../net/MessageId":55,"../net/WebSocketManager":56,"../ToastController":39,"../util/Config":80,"../util/Tools":77,"./HallCenterLayController":33},"path":"preview-scripts/assets/scripts/hall/HallParentController.js"},{"deps":{"../util/Config":80,"../util/Tools":77},"path":"preview-scripts/assets/scripts/hall/InfoDialogController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/ToastController.js"},{"deps":{"../net/MessageId":55,"../net/WebSocketManager":56,"../util/Config":80,"../util/Tools":77},"path":"preview-scripts/assets/scripts/hall/KickOutDialogController.js"},{"deps":{"../../meshTools/tools/Publish":22,"../bean/GlobalBean":4,"../common/EventCenter":53,"../common/GameMgr":21,"../net/MessageBaseBean":54,"../pfb/MatchItemController":59,"../util/Config":80,"../util/Tools":77},"path":"preview-scripts/assets/scripts/hall/MatchParentController.js"},{"deps":{"../common/GameMgr":21,"../net/MessageId":55,"../net/WebSocketManager":56,"../util/Config":80,"../util/Tools":77},"path":"preview-scripts/assets/scripts/hall/LeaveDialogController.js"},{"deps":{"../bean/GlobalBean":4,"../util/Tools":77},"path":"preview-scripts/assets/scripts/hall/PlayerLayoutController.js"},{"deps":{"../../meshTools/tools/Publish":22,"../util/AudioManager":79,"../util/Config":80,"../util/LocalStorageManager":69,"../util/Tools":77},"path":"preview-scripts/assets/scripts/hall/SettingDialogController.js"},{"deps":{"../bean/GlobalBean":4,"../util/Config":80,"../util/Tools":77},"path":"preview-scripts/assets/scripts/hall/HallAutoController.js"},{"deps":{"../../GlobalManagerController":18,"./LevelSelectController":6,"../../net/MessageId":55,"../../net/WebSocketManager":56},"path":"preview-scripts/assets/scripts/hall/Level/LevelSelectPageController.js"},{"deps":{"./LevelSelectController":6},"path":"preview-scripts/assets/scripts/hall/Level/LevelSelectExample.js"},{"deps":{"./LevelSelectController":6},"path":"preview-scripts/assets/scripts/hall/Level/LevelItemController.js"},{"deps":{"./HttpUtils":50,"./MessageBaseBean":54,"./GameServerUrl":11,"../../meshTools/MeshTools":73,"../common/GameMgr":21,"../common/EventCenter":53},"path":"preview-scripts/assets/scripts/net/HttpManager.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/HttpUtils.js"},{"deps":{"../common/GameMgr":21,"../util/Config":80,"../util/Tools":77},"path":"preview-scripts/assets/scripts/hall/TopUpDialogController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/hall/Level/ScrollViewHelper.js"},{"deps":{"../../meshTools/Singleton":8,"./GameMgr":21},"path":"preview-scripts/assets/scripts/common/EventCenter.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/MessageBaseBean.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/MessageId.js"},{"deps":{"../../meshTools/Singleton":8,"../common/EventCenter":53,"../common/GameMgr":21,"./WebSocketTool":57},"path":"preview-scripts/assets/scripts/net/WebSocketManager.js"},{"deps":{"./MessageBaseBean":54,"./MessageId":55,"../util/Tools":77,"../../meshTools/Singleton":8,"../common/EventCenter":53,"../common/GameMgr":21},"path":"preview-scripts/assets/scripts/net/WebSocketTool.js"},{"deps":{},"path":"preview-scripts/assets/scripts/pfb/InfoItemOneController.js"},{"deps":{"../util/NickNameLabel":70,"../util/Tools":77},"path":"preview-scripts/assets/scripts/pfb/MatchItemController.js"},{"deps":{"../util/Tools":77},"path":"preview-scripts/assets/scripts/pfb/PlayerGameController .js"},{"deps":{"../bean/GlobalBean":4,"../util/NickNameLabel":70,"../util/Tools":77},"path":"preview-scripts/assets/scripts/pfb/PlayerScoreController.js"},{"deps":{"../../meshTools/tools/Publish":22,"../util/Config":80,"../util/NickNameLabel":70,"../util/Tools":77},"path":"preview-scripts/assets/scripts/pfb/CongratsItemController.js"},{"deps":{"../../net/WebSocketManager":56,"../../net/MessageId":55},"path":"preview-scripts/assets/scripts/game/Chess/SingleChessBoardController.js"},{"deps":{"../common/EventCenter":53,"../common/GameMgr":21,"../net/MessageBaseBean":54,"../util/Config":80},"path":"preview-scripts/assets/scripts/start_up/StartUpCenterController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/test/AnimationTest.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/IHttpMsgBody.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/ErrorCode.js"},{"deps":{"../util/NickNameLabel":70,"../util/Tools":77},"path":"preview-scripts/assets/scripts/pfb/SeatItemController.js"},{"deps":{"../../meshTools/Singleton":8},"path":"preview-scripts/assets/scripts/util/LocalStorageManager.js"},{"deps":{},"path":"preview-scripts/assets/scripts/util/NickNameLabel.js"},{"deps":{},"path":"preview-scripts/assets/scripts/util/Dictionary.js"},{"deps":{"../common/GameMgr":21,"../common/EventCenter":53,"../net/MessageId":55,"../bean/GlobalBean":4},"path":"preview-scripts/assets/scripts/test/NoticeRoundStartTest.js"},{"deps":{"./tools/Publish":22},"path":"preview-scripts/assets/meshTools/MeshTools.js"},{"deps":{},"path":"preview-scripts/assets/resources/i18n/zh_CN.js"},{"deps":{},"path":"preview-scripts/assets/resources/i18n/en.js"},{"deps":{},"path":"preview-scripts/assets/scripts/util/BlockingQueue.js"},{"deps":{"./AudioManager":79,"./Config":80},"path":"preview-scripts/assets/scripts/util/Tools.js"},{"deps":{"./Level/LevelSelectController":6},"path":"preview-scripts/assets/scripts/hall/LevelSelectDemo.js"},{"deps":{"./AudioMgr":15,"./LocalStorageManager":69},"path":"preview-scripts/assets/scripts/util/AudioManager.js"},{"deps":{},"path":"preview-scripts/assets/scripts/util/Config.js"},{"deps":{"../MeshTools":73,"../BaseSDK":16,"../../scripts/net/MessageBaseBean":54,"../../scripts/common/GameMgr":21,"../../scripts/common/EventCenter":53,"MeshSdk":3},"path":"preview-scripts/assets/meshTools/tools/MeshSdkApi.js"}];
var entries = ["preview-scripts/__qc_index__.js"];
var bundleScript = 'preview-scripts/__qc_bundle__.js';

/**
 * Notice: This file can not use ES6 (for IE 11)
 */
var modules = {};
var name2path = {};

// Will generated by module.js plugin
// var scripts = ${scripts};
// var entries = ${entries};
// var bundleScript = ${bundleScript};

if (typeof global === 'undefined') {
    window.global = window;
}

var isJSB = typeof jsb !== 'undefined';

function getXMLHttpRequest () {
    return window.XMLHttpRequest ? new window.XMLHttpRequest() : new ActiveXObject('MSXML2.XMLHTTP');
}

function downloadText(url, callback) {
    if (isJSB) {
        var result = jsb.fileUtils.getStringFromFile(url);
        callback(null, result);
        return;
    }

    var xhr = getXMLHttpRequest(),
        errInfo = 'Load text file failed: ' + url;
    xhr.open('GET', url, true);
    if (xhr.overrideMimeType) xhr.overrideMimeType('text\/plain; charset=utf-8');
    xhr.onload = function () {
        if (xhr.readyState === 4) {
            if (xhr.status === 200 || xhr.status === 0) {
                callback(null, xhr.responseText);
            }
            else {
                callback({status:xhr.status, errorMessage:errInfo + ', status: ' + xhr.status});
            }
        }
        else {
            callback({status:xhr.status, errorMessage:errInfo + '(wrong readyState)'});
        }
    };
    xhr.onerror = function(){
        callback({status:xhr.status, errorMessage:errInfo + '(error)'});
    };
    xhr.ontimeout = function(){
        callback({status:xhr.status, errorMessage:errInfo + '(time out)'});
    };
    xhr.send(null);
};

function loadScript (src, cb) {
    if (typeof require !== 'undefined') {
        require(src);
        return cb();
    }

    // var timer = 'load ' + src;
    // console.time(timer);

    var scriptElement = document.createElement('script');

    function done() {
        // console.timeEnd(timer);
        // deallocation immediate whatever
        scriptElement.remove();
    }

    scriptElement.onload = function () {
        done();
        cb();
    };
    scriptElement.onerror = function () {
        done();
        var error = 'Failed to load ' + src;
        console.error(error);
        cb(new Error(error));
    };
    scriptElement.setAttribute('type','text/javascript');
    scriptElement.setAttribute('charset', 'utf-8');
    scriptElement.setAttribute('src', src);

    document.head.appendChild(scriptElement);
}

function loadScripts (srcs, cb) {
    var n = srcs.length;

    srcs.forEach(function (src) {
        loadScript(src, function () {
            n--;
            if (n === 0) {
                cb();
            }
        });
    })
}

function formatPath (path) {
    let destPath = window.__quick_compile_project__.destPath;
    if (destPath) {
        let prefix = 'preview-scripts';
        if (destPath[destPath.length - 1] === '/') {
            prefix += '/';
        }
        path = path.replace(prefix, destPath);
    }
    return path;
}

window.__quick_compile_project__ = {
    destPath: '',

    registerModule: function (path, module) {
        path = formatPath(path);
        modules[path].module = module;
    },

    registerModuleFunc: function (path, func) {
        path = formatPath(path);
        modules[path].func = func;

        var sections = path.split('/');
        var name = sections[sections.length - 1];
        name = name.replace(/\.(?:js|ts|json)$/i, '');
        name2path[name] = path;
    },

    require: function (request, path) {
        var m, requestScript;

        path = formatPath(path);
        if (path) {
            m = modules[path];
            if (!m) {
                console.warn('Can not find module for path : ' + path);
                return null;
            }
        }

        if (m) {
            let depIndex = m.deps[request];
            // dependence script was excluded
            if (depIndex === -1) {
                return null;
            }
            else {
                requestScript = scripts[ m.deps[request] ];
            }
        }
        
        let requestPath = '';
        if (!requestScript) {
            // search from name2path when request is a dynamic module name
            if (/^[\w- .]*$/.test(request)) {
                requestPath = name2path[request];
            }

            if (!requestPath) {
                if (CC_JSB) {
                    return require(request);
                }
                else {
                    console.warn('Can not find deps [' + request + '] for path : ' + path);
                    return null;
                }
            }
        }
        else {
            requestPath = formatPath(requestScript.path);
        }

        let requestModule = modules[requestPath];
        if (!requestModule) {
            console.warn('Can not find request module for path : ' + requestPath);
            return null;
        }

        if (!requestModule.module && requestModule.func) {
            requestModule.func();
        }

        if (!requestModule.module) {
            console.warn('Can not find requestModule.module for path : ' + path);
            return null;
        }

        return requestModule.module.exports;
    },

    run: function () {
        entries.forEach(function (entry) {
            entry = formatPath(entry);
            var module = modules[entry];
            if (!module.module) {
                module.func();
            }
        });
    },

    load: function (cb) {
        var self = this;

        var srcs = scripts.map(function (script) {
            var path = formatPath(script.path);
            modules[path] = script;

            if (script.mtime) {
                path += ("?mtime=" + script.mtime);
            }
            return path;
        });

        console.time && console.time('load __quick_compile_project__');
        // jsb can not analysis sourcemap, so keep separate files.
        if (bundleScript && !isJSB) {
            downloadText(formatPath(bundleScript), function (err, bundleSource) {
                console.timeEnd && console.timeEnd('load __quick_compile_project__');
                if (err) {
                    console.error(err);
                    return;
                }

                let evalTime = 'eval __quick_compile_project__ : ' + srcs.length + ' files';
                console.time && console.time(evalTime);
                var sources = bundleSource.split('\n//------QC-SOURCE-SPLIT------\n');
                for (var i = 0; i < sources.length; i++) {
                    if (sources[i]) {
                        window.eval(sources[i]);
                        // not sure why new Function cannot set breakpoints precisely
                        // new Function(sources[i])()
                    }
                }
                self.run();
                console.timeEnd && console.timeEnd(evalTime);
                cb();
            })
        }
        else {
            loadScripts(srcs, function () {
                self.run();
                console.timeEnd && console.timeEnd('load __quick_compile_project__');
                cb();
            });
        }
    }
};

// Polyfill for IE 11
if (!('remove' in Element.prototype)) {
    Element.prototype.remove = function () {
        if (this.parentNode) {
            this.parentNode.removeChild(this);
        }
    };
}
})();
    