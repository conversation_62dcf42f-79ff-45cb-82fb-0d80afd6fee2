{"version": 3, "sources": ["assets/scripts/game/AIManagedDialogController.ts"], "names": [], "mappings": ";;;;;AAAA,aAAa;AACb,kCAAkC;;;;;;;;;;;;;;;;;;;;;AAElC,4DAA2D;AAC3D,8CAA6C;AAC7C,yCAAwC;AAElC,IAAA,KAAwB,EAAE,CAAC,UAAU,EAAnC,OAAO,aAAA,EAAE,QAAQ,cAAkB,CAAC;AAG5C;IAAuD,6CAAY;IAAnE;QAAA,qEAgQC;QA7PG,aAAO,GAAY,IAAI,CAAC,CAAC,MAAM;QAG/B,cAAQ,GAAY,IAAI,CAAC,CAAC,gBAAgB;QAIlC,eAAS,GAAY,KAAK,CAAC,CAAC,SAAS;;IAsPjD,CAAC;IApPG,yCAAK,GAAL;QACI,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAC;QAErD,SAAS;QACT,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QAEzB,SAAS;QACT,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QACvB,OAAO,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;QAC5D,OAAO,CAAC,GAAG,CAAC,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;QACrE,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;QAExE,0BAA0B;QAC1B,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACf,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC;YACtC,IAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;YAClD,IAAI,MAAM,EAAE;gBACR,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;gBACtB,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;aAC5C;iBAAM;gBACH,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;aAClC;SACJ;QAED,gBAAgB;QAChB,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,oCAAoC;YACpC,IAAM,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC,gBAAgB,CAAC,CAAC;YACzE,IAAI,gBAAgB,EAAE;gBAClB,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;gBAClD,gBAAgB,CAAC,OAAO,GAAG,KAAK,CAAC;aACpC;YAED,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;YACtE,OAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YACrD,OAAO,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,GAAG,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YACvE,OAAO,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;SAClD;aAAM;YACH,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;SAChC;QAED,qBAAqB;QACrB,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;IACtE,CAAC;IAED;;OAEG;IACH,wCAAI,GAAJ;QACI,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;QAEpD,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;YAC7B,OAAO;SACV;QAED,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;QAC3B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QAEtB,SAAS;QACT,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QACxB,OAAO,CAAC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAE9D,YAAY;QACZ,IAAI,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;QAC9B,IAAI,WAAW,GAAG,EAAE,CAAC;QACrB,OAAO,MAAM,EAAE;YACX,WAAW,CAAC,IAAI,CAAI,MAAM,CAAC,IAAI,iBAAY,MAAM,CAAC,MAAM,MAAG,CAAC,CAAC;YAC7D,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;SAC1B;QACD,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;QAE/C,gCAAgC;QAChC,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,IAAM,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC,gBAAgB,CAAC,CAAC;YACzE,IAAI,gBAAgB,EAAE;gBAClB,gBAAgB,CAAC,OAAO,GAAG,KAAK,CAAC;gBACjC,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;aAC5C;SACJ;QAED,SAAS;QACT,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAE1C,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;QAC/B,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAChD,OAAO,CAAC,GAAG,CAAC,2BAA2B,EAAE,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QACtE,OAAO,CAAC,GAAG,CAAC,kBAAkB,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACpD,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC9C,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAElD,UAAU;QACV,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC;YACvB,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,CAAC,CAAC;YACzB,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;SAC3B;aAAM;YACH,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;SACnC;QAED,SAAS;QACT,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACzB,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,wCAAI,GAAJ;QAAA,iBAoBC;QAnBG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB,OAAO;SACV;QAED,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QAEvB,iCAAiC;QACjC,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,IAAM,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC,gBAAgB,CAAC,CAAC;YACzE,IAAI,gBAAgB,EAAE;gBAClB,gBAAgB,CAAC,OAAO,GAAG,IAAI,CAAC;gBAChC,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;aAC3C;SACJ;QAED,SAAS;QACT,IAAI,CAAC,iBAAiB,CAAC;YACnB,KAAI,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QAC7B,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACK,qDAAiB,GAAzB;QACI,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC;QAEtC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACf,OAAO,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;YACpC,OAAO;SACV;QAED,OAAO,CAAC,GAAG,CAAC,oBAAoB,EAAE,eAAM,CAAC,eAAe,CAAC,CAAC;QAE1D,WAAW;QACX,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC;aACjB,EAAE,CAAC,eAAM,CAAC,eAAe,EAAE;YACxB,KAAK,EAAE,CAAC;YACR,OAAO,EAAE,GAAG;SACf,EAAE;YACC,MAAM,EAAE,SAAS;SACpB,CAAC;aACD,IAAI,CAAC;YACF,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;QAC9B,CAAC,CAAC;aACD,KAAK,EAAE,CAAC;IACjB,CAAC;IAED;;;OAGG;IACK,qDAAiB,GAAzB,UAA0B,QAAqB;QAC3C,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACf,IAAI,QAAQ;gBAAE,QAAQ,EAAE,CAAC;YACzB,OAAO;SACV;QAED,WAAW;QACX,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC;aACjB,EAAE,CAAC,eAAM,CAAC,eAAe,EAAE;YACxB,KAAK,EAAE,CAAC;YACR,OAAO,EAAE,CAAC;SACb,EAAE;YACC,MAAM,EAAE,QAAQ;SACnB,CAAC;aACD,IAAI,CAAC;YACF,IAAI,QAAQ;gBAAE,QAAQ,EAAE,CAAC;QAC7B,CAAC,CAAC;aACD,KAAK,EAAE,CAAC;IACjB,CAAC;IAED;;OAEG;IACK,+CAAW,GAAnB;QACI,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QACvB,IAAI,CAAC,WAAW,EAAE,CAAC;IACvB,CAAC;IAED;;OAEG;IACK,+CAAW,GAAnB;QACI,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACtB,IAAI,CAAC,WAAW,EAAE,CAAC;IACvB,CAAC;IAED;;OAEG;IACK,+CAAW,GAAnB;QACI,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;YAC5B,OAAO;SACV;QAED,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;QAEjC,aAAa;QACb,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAE9B,mBAAmB;QACnB,IAAI,CAAC,IAAI,EAAE,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,0DAAsB,GAA9B;QACI,IAAM,UAAU,GAAG;QACf,eAAe;SAClB,CAAC;QAEF,mCAAgB,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,qBAAS,CAAC,yBAAyB,EAAE,UAAU,CAAC,CAAC;QACxF,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,6CAAS,GAAT;QACI,OAAO,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;IAC9C,CAAC;IAED,6CAAS,GAAT;QACI,SAAS;QACT,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;SAC1E;QAED,IAAI,IAAI,CAAC,IAAI,EAAE;YACX,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;SACtE;IACL,CAAC;IA5PD;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;8DACM;IAGxB;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;+DACO;IANR,yBAAyB;QAD7C,OAAO;OACa,yBAAyB,CAgQ7C;IAAD,gCAAC;CAhQD,AAgQC,CAhQsD,EAAE,CAAC,SAAS,GAgQlE;kBAhQoB,yBAAyB", "file": "", "sourceRoot": "/", "sourcesContent": ["// AI托管中页面控制器\n// 当玩家进入AI托管状态时显示，点击屏幕任何位置发送取消托管消息\n\nimport { WebSocketManager } from \"../net/WebSocketManager\";\nimport { MessageId } from \"../net/MessageId\";\nimport { Config } from \"../util/Config\";\n\nconst { ccclass, property } = cc._decorator;\n\n@ccclass\nexport default class AIManagedDialogController extends cc.Component {\n\n    @property(cc.Node)\n    boardBg: cc.Node = null; // 背景板\n\n    @property(cc.Node)\n    maskNode: cc.Node = null; // 遮罩节点，用于接收点击事件\n\n   \n\n    private isShowing: boolean = false; // 是否正在显示\n\n    start() {\n        console.log(\"AIManagedDialogController start() 被调用\");\n\n        // 初始化时隐藏\n        this.node.active = false;\n\n        // 检查节点配置\n        console.log(\"检查节点配置:\");\n        console.log(\"- node:\", this.node ? this.node.name : \"null\");\n        console.log(\"- boardBg:\", this.boardBg ? this.boardBg.name : \"null\");\n        console.log(\"- maskNode:\", this.maskNode ? this.maskNode.name : \"null\");\n\n        // 如果 boardBg 没有设置，尝试查找子节点\n        if (!this.boardBg) {\n            console.log(\"boardBg 未设置，尝试查找子节点...\");\n            const bgNode = this.node.getChildByName('bot_bg');\n            if (bgNode) {\n                this.boardBg = bgNode;\n                console.log(\"找到 bot_bg 子节点，设置为 boardBg\");\n            } else {\n                console.warn(\"未找到 bot_bg 子节点\");\n            }\n        }\n\n        // 为遮罩节点添加点击事件监听\n        if (this.maskNode) {\n            // 检查是否有 BlockInputEvents 组件，如果有则禁用它\n            const blockInputEvents = this.maskNode.getComponent(cc.BlockInputEvents);\n            if (blockInputEvents) {\n                console.log(\"发现 BlockInputEvents 组件，将其禁用以允许点击事件\");\n                blockInputEvents.enabled = false;\n            }\n\n            this.maskNode.on(cc.Node.EventType.TOUCH_END, this.onMaskClick, this);\n            console.log(\"遮罩节点点击事件已设置，节点名称:\", this.maskNode.name);\n            console.log(\"遮罩节点大小:\", this.maskNode.width, \"x\", this.maskNode.height);\n            console.log(\"遮罩节点位置:\", this.maskNode.position);\n        } else {\n            console.warn(\"maskNode 未设置\");\n        }\n\n        // 同时为主节点添加点击事件监听作为备用\n        this.node.on(cc.Node.EventType.TOUCH_END, this.onNodeClick, this);\n    }\n\n    /**\n     * 显示托管中页面\n     */\n    show() {\n        console.log(\"AIManagedDialogController.show() 被调用\");\n\n        if (this.isShowing) {\n            console.log(\"托管页面已经在显示中，跳过\");\n            return;\n        }\n\n        console.log(\"开始显示托管页面...\");\n        this.isShowing = true;\n\n        // 强制激活节点\n        this.node.active = true;\n        console.log(\"设置 node.active = true 后，实际值:\", this.node.active);\n\n        // 检查父节点是否激活\n        let parent = this.node.parent;\n        let parentChain = [];\n        while (parent) {\n            parentChain.push(`${parent.name}(active: ${parent.active})`);\n            parent = parent.parent;\n        }\n        console.log(\"父节点链:\", parentChain.join(\" -> \"));\n\n        // 禁用 BlockInputEvents 组件以允许点击事件\n        if (this.maskNode) {\n            const blockInputEvents = this.maskNode.getComponent(cc.BlockInputEvents);\n            if (blockInputEvents) {\n                blockInputEvents.enabled = false;\n                console.log(\"显示时禁用 BlockInputEvents 组件\");\n            }\n        }\n\n        // 强制刷新节点\n        this.node.setPosition(this.node.position);\n\n        console.log(\"托管页面节点已激活，最终状态:\");\n        console.log(\"- node.active:\", this.node.active);\n        console.log(\"- node.activeInHierarchy:\", this.node.activeInHierarchy);\n        console.log(\"- node.position:\", this.node.position);\n        console.log(\"- node.scale:\", this.node.scale);\n        console.log(\"- node.opacity:\", this.node.opacity);\n\n        // 初始化动画状态\n        if (this.boardBg) {\n            this.boardBg.scale = 0;\n            this.boardBg.opacity = 0;\n            console.log(\"背景板初始化完成\");\n        } else {\n            console.warn(\"❌ boardBg 节点未设置\");\n        }\n\n        // 执行显示动画\n        this.playShowAnimation();\n        console.log(\"托管页面显示动画已启动\");\n    }\n\n    /**\n     * 隐藏托管中页面\n     */\n    hide() {\n        if (!this.isShowing) {\n            return;\n        }\n\n        this.isShowing = false;\n\n        // 重新启用 BlockInputEvents 组件（如果存在）\n        if (this.maskNode) {\n            const blockInputEvents = this.maskNode.getComponent(cc.BlockInputEvents);\n            if (blockInputEvents) {\n                blockInputEvents.enabled = true;\n                console.log(\"重新启用 BlockInputEvents 组件\");\n            }\n        }\n\n        // 执行隐藏动画\n        this.playHideAnimation(() => {\n            this.node.active = false;\n        });\n    }\n\n    /**\n     * 播放显示动画\n     */\n    private playShowAnimation() {\n        console.log(\"playShowAnimation 开始执行\");\n\n        if (!this.boardBg) {\n            console.warn(\"❌ boardBg 为空，无法播放动画\");\n            return;\n        }\n\n        console.log(\"开始播放托管页面显示动画，动画时间:\", Config.dialogScaleTime);\n\n        // 缩放和透明度动画\n        cc.tween(this.boardBg)\n            .to(Config.dialogScaleTime, {\n                scale: 1,\n                opacity: 255\n            }, {\n                easing: 'backOut'\n            })\n            .call(() => {\n                console.log(\"托管页面显示动画完成\");\n            })\n            .start();\n    }\n\n    /**\n     * 播放隐藏动画\n     * @param callback 动画完成回调\n     */\n    private playHideAnimation(callback?: () => void) {\n        if (!this.boardBg) {\n            if (callback) callback();\n            return;\n        }\n\n        // 缩放和透明度动画\n        cc.tween(this.boardBg)\n            .to(Config.dialogScaleTime, { \n                scale: 0, \n                opacity: 0 \n            }, { \n                easing: 'backIn' \n            })\n            .call(() => {\n                if (callback) callback();\n            })\n            .start();\n    }\n\n    /**\n     * 遮罩点击事件处理\n     */\n    private onMaskClick() {\n        console.log(\"遮罩节点被点击\");\n        this.handleClick();\n    }\n\n    /**\n     * 主节点点击事件处理\n     */\n    private onNodeClick() {\n        console.log(\"主节点被点击\");\n        this.handleClick();\n    }\n\n    /**\n     * 统一的点击处理逻辑\n     */\n    private handleClick() {\n        if (!this.isShowing) {\n            console.log(\"托管页面未显示，忽略点击\");\n            return;\n        }\n\n        console.log(\"托管页面点击，发送取消AI托管消息\");\n\n        // 发送取消AI托管消息\n        this.sendCancelAIManagement();\n\n        // 立即隐藏页面（不等待服务器响应）\n        this.hide();\n    }\n\n    /**\n     * 发送取消AI托管消息\n     */\n    private sendCancelAIManagement() {\n        const cancelData = {\n            // 可以根据需要添加其他参数\n        };\n\n        WebSocketManager.GetInstance().sendMsg(MessageId.MsgTypeCancelAIManagement, cancelData);\n        console.log(\"已发送取消AI托管消息\");\n    }\n\n    /**\n     * 检查是否正在显示\n     */\n    isVisible(): boolean {\n        return this.isShowing && this.node.active;\n    }\n\n    onDestroy() {\n        // 清理事件监听\n        if (this.maskNode) {\n            this.maskNode.off(cc.Node.EventType.TOUCH_END, this.onMaskClick, this);\n        }\n\n        if (this.node) {\n            this.node.off(cc.Node.EventType.TOUCH_END, this.onNodeClick, this);\n        }\n    }\n}\n"]}